# Sistema de Ponto Eletrônico CLT

Sistema completo de registro de ponto eletrônico em conformidade com a CLT brasileira, incluindo aplicativo mobile Flutter, frontend web React e backend FastAPI.

## 🏗️ Arquitetura do Sistema

```
ponto/
├── backend/           # API FastAPI + Python
├── frontend-web/      # React + TypeScript + Tailwind
├── mobile-app/        # Flutter (Android/iOS)
├── admin-panel/       # Painel administrativo RH
├── docs/             # Documentação e manuais
├── docker/           # Configurações Docker
└── shared/           # Tipos e utilitários compartilhados
```

## 🚀 Tecnologias

- **Backend**: FastAPI + Python + PostgreSQL + Redis
- **Frontend Web**: React + TypeScript + Tailwind CSS + Vite
- **Mobile**: Flutter + Dart
- **Infraestrutura**: Docker + Docker Compose
- **Armazenamento**: MinIO (fotos e documentos)
- **Autenticação**: JWT + Refresh Tokens

## 📋 Funcionalidades Principais

### 📱 Aplicativo Mobile
- ✅ Registro de ponto com selfie e GPS
- ✅ Validação facial anti-fraude
- ✅ Modo offline com sincronização
- ✅ Comprovante digital criptografado
- ✅ Alertas de intervalo obrigatório CLT

### 🌐 Frontend Web (Funcionários)
- ✅ Dashboard pessoal com espelho de ponto
- ✅ Saldos de horas (banco, extras, débitos)
- ✅ Solicitações de correção com anexos
- ✅ Histórico completo de registros
- ✅ Dark mode e acessibilidade

### 👥 Painel Administrativo (RH)
- ✅ Aprovação de solicitações
- ✅ Relatórios CLT e exportação eSocial
- ✅ Gestão de funcionários e turnos
- ✅ Auditoria e logs de alterações
- ✅ Assinatura digital para ajustes

## ⚖️ Conformidade Legal

### CLT (Art. 74)
- 🔒 Registros imutáveis (apenas via solicitação)
- 📝 Audit trail completo
- 🌐 Registro de IP e user-agent
- 📊 Relatórios para fiscalização

### LGPD
- 🗑️ Exclusão de fotos após 6 meses
- 🔐 Criptografia de dados sensíveis
- 📋 Consentimento explícito
- 🔍 Portabilidade de dados

## 🛠️ Instalação e Desenvolvimento

### Pré-requisitos
- Python 3.11+
- Node.js 18+
- Flutter 3.0+
- Docker & Docker Compose
- PostgreSQL 14+

### Configuração Rápida
```bash
# Clone o repositório
git clone https://github.com/reginaldobertoluci/ponto.git
cd ponto

# Configure as variáveis de ambiente
cp .env.example .env

# Inicie os serviços com Docker
docker-compose up -d

# Instale dependências do backend
cd backend && pip install -r requirements.txt

# Execute o backend
python run_dev.py

# Instale dependências do frontend (em outro terminal)
cd frontend-web && npm install

# Execute o frontend
npm run dev

# Configure o app mobile (quando disponível)
cd mobile-app && flutter pub get
```

### URLs de Acesso
- **Frontend Web**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Admin Panel**: http://localhost:5174 (em desenvolvimento)
- **MinIO Console**: http://localhost:9001

## 📚 Documentação

- [Manual de Implantação](docs/deployment.md)
- [Checklist Auditoria CLT](docs/clt-compliance.md)
- [API Documentation](docs/api.md)
- [Guia do Desenvolvedor](docs/development.md)

## 🧪 Testes

```bash
# Backend
cd backend && pytest

# Frontend
cd frontend-web && npm run test

# Mobile
cd mobile-app && flutter test
```

## 📄 Licença

Este projeto está licenciado sob a MIT License - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📞 Suporte

Para suporte técnico ou dúvidas sobre conformidade CLT, entre em contato através dos issues do GitHub.

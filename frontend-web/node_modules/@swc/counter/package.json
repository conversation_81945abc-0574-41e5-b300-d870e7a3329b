{"name": "@swc/counter", "packageManager": "pnpm@8.6.7", "main": "index.js", "version": "0.1.3", "description": "Downloade counter for the swc project", "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/swc-project/pkgs.git"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "keywords": ["swc", "download", "counter"], "author": "강동윤 <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/swc-project/swc/issues"}, "homepage": "https://swc.rs"}
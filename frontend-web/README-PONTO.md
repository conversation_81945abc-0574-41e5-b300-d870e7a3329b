# Frontend Web - Ponto Eletrônico CLT

Interface web desenvolvida com React + TypeScript + Tailwind CSS para funcionários do sistema de ponto eletrônico.

## 🚀 Tecnologias

- **React** 18 - Biblioteca para interfaces de usuário
- **TypeScript** - Superset tipado do JavaScript
- **Tailwind CSS** - Framework CSS utilitário
- **Vite** - Build tool moderna e rápida
- **React Router** - Roteamento client-side
- **Zustand** - Gerenciamento de estado
- **React Query** - Gerenciamento de estado do servidor
- **React Hook Form** - Formulários performáticos
- **Zod** - Validação de schemas
- **Axios** - Cliente HTTP

## 📋 Funcionalidades

### 🔐 Autenticação
- Login com email e senha
- Logout seguro
- Renovação automática de tokens
- Proteção de rotas

### 📊 Dashboard
- Visão geral dos registros do dia
- Estatísticas semanais e mensais
- Próximo registro esperado
- Alertas e notificações

### ⏰ Registros de Ponto
- Visualização de registros
- Filtros por data e tipo
- Detalhes com localização e foto
- Histórico completo

### 📝 Solicitações de Ajuste
- Criação de solicitações
- Upload de anexos
- Acompanhamento de status
- Histórico de aprovações

## 🛠️ Instalação

### Pré-requisitos
- Node.js 18+
- npm ou yarn

### Configuração

1. **Instale as dependências**
```bash
npm install
```

2. **Configure as variáveis de ambiente**
```bash
cp .env.example .env
```

3. **Execute em desenvolvimento**
```bash
npm run dev
```

4. **Acesse a aplicação**
```
http://localhost:5173
```

## 🏗️ Build e Deploy

### Build para produção
```bash
npm run build
```

### Preview da build
```bash
npm run preview
```

### Docker
```bash
docker build -t ponto-frontend .
docker run -p 80:80 ponto-frontend
```

## 📁 Estrutura do Projeto

```
frontend-web/
├── src/
│   ├── components/      # Componentes reutilizáveis
│   ├── pages/          # Páginas da aplicação
│   ├── layouts/        # Layouts (Auth, App)
│   ├── hooks/          # Custom hooks
│   ├── services/       # Serviços (API, etc.)
│   ├── store/          # Estado global (Zustand)
│   ├── types/          # Tipos TypeScript
│   ├── utils/          # Utilitários
│   └── App.tsx         # Componente principal
├── tailwind.config.js  # Configuração Tailwind
├── vite.config.ts      # Configuração Vite
└── package.json        # Dependências
```

## 🎨 Design System

### Cores
- **Primary**: Azul (#3B82F6)
- **Success**: Verde (#22C55E)
- **Warning**: Amarelo (#F59E0B)
- **Error**: Vermelho (#EF4444)

### Componentes
- **Button**: Variantes (primary, secondary, outline, ghost)
- **Input**: Com validação e ícones
- **Card**: Container para conteúdo
- **Toast**: Notificações temporárias

## 🔧 Configurações

### Variáveis de Ambiente

| Variável | Descrição | Padrão |
|----------|-----------|---------|
| `VITE_API_URL` | URL da API backend | http://localhost:8000 |
| `VITE_APP_NAME` | Nome da aplicação | Ponto Eletrônico CLT |

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch: `git checkout -b feature/nova-funcionalidade`
3. Commit: `git commit -m 'Adiciona nova funcionalidade'`
4. Push: `git push origin feature/nova-funcionalidade`
5. Abra um Pull Request

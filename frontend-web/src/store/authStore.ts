import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthState, User, LoginRequest } from '../types';
import { apiService } from '../services/api';
import { toast } from 'react-hot-toast';

interface AuthStore extends AuthState {
  // Actions
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  updateUser: (user: Partial<User>) => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,

      // Actions
      login: async (credentials: LoginRequest) => {
        try {
          set({ isLoading: true });

          const response = await apiService.login(credentials);

          // Armazenar tokens no localStorage
          localStorage.setItem('access_token', response.access_token);
          localStorage.setItem('refresh_token', response.refresh_token);

          set({
            user: response.user,
            token: response.access_token,
            refreshToken: response.refresh_token,
            isAuthenticated: true,
            isLoading: false,
          });

          toast.success('Login realizado com sucesso!');
        } catch (error: any) {
          set({ isLoading: false });
          const message = error.response?.data?.message || 'Erro ao fazer login';
          toast.error(message);
          throw error;
        }
      },

      logout: () => {
        // Chamar API de logout (sem aguardar)
        apiService.logout().catch(() => {
          // Ignorar erros de logout
        });

        // Limpar estado
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: false,
        });

        // Limpar localStorage
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');

        toast.success('Logout realizado com sucesso!');
      },

      refreshUser: async () => {
        try {
          const user = await apiService.getCurrentUser();
          set({ user });
        } catch (error) {
          // Se não conseguir buscar o usuário, fazer logout
          get().logout();
        }
      },

      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData },
          });
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Hook para verificar permissões
export const usePermissions = () => {
  const user = useAuthStore((state) => state.user);

  const hasRole = (roles: string | string[]) => {
    if (!user) return false;
    const roleArray = Array.isArray(roles) ? roles : [roles];
    return roleArray.includes(user.role);
  };

  const isAdmin = () => hasRole('admin');
  const isHR = () => hasRole(['hr', 'admin']);
  const isSupervisor = () => hasRole(['supervisor', 'hr', 'admin']);
  const isEmployee = () => hasRole('employee');

  const canApproveAdjustments = () => hasRole(['supervisor', 'hr', 'admin']);
  const canManageUsers = () => hasRole(['hr', 'admin']);
  const canViewReports = () => hasRole(['supervisor', 'hr', 'admin']);
  const canExportData = () => hasRole(['hr', 'admin']);

  return {
    user,
    hasRole,
    isAdmin,
    isHR,
    isSupervisor,
    isEmployee,
    canApproveAdjustments,
    canManageUsers,
    canViewReports,
    canExportData,
  };
};

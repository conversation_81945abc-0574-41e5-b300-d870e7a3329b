// Tipos de usuário e autenticação
export interface User {
  id: string;
  email: string;
  name: string;
  cpf_masked: string;
  phone?: string;
  employee_id?: string;
  role: UserRole;
  department?: string;
  position?: string;
  is_active: boolean;
  is_verified: boolean;
  work_schedule?: WorkSchedule;
  notification_settings?: NotificationSettings;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'employee' | 'supervisor' | 'hr' | 'admin';

export interface WorkSchedule {
  name: string;
  start_time: string;
  end_time: string;
  lunch_start_time: string;
  lunch_end_time: string;
  work_days: number[];
  tolerance_minutes: number;
}

export interface NotificationSettings {
  email_enabled: boolean;
  push_enabled: boolean;
  sms_enabled: boolean;
  reminder_before_shift: number;
  overtime_alert: boolean;
}

// Tipos de registro de ponto
export interface TimeRecord {
  id: string;
  user_id: string;
  type: TimeRecordType;
  timestamp: string;
  location: Location;
  photo_url?: string;
  device_info: DeviceInfo;
  hash: string;
  is_manual: boolean;
  approved_by?: string;
  approved_at?: string;
  created_at: string;
}

export type TimeRecordType = 'entry' | 'exit' | 'lunch_start' | 'lunch_end';

export interface Location {
  latitude: number;
  longitude: number;
  address: string;
  accuracy?: number;
}

export interface DeviceInfo {
  ip?: string;
  user_agent?: string;
  device_id?: string;
  platform?: string;
}

// Tipos de solicitação de ajuste
export interface AdjustmentRequest {
  id: string;
  user_id: string;
  original_record_id?: string;
  requested_timestamp: string;
  requested_type: TimeRecordType;
  reason: string;
  attachments: string[];
  status: AdjustmentStatus;
  reviewed_by?: string;
  reviewed_at?: string;
  review_notes?: string;
  created_at: string;
  updated_at: string;
}

export type AdjustmentStatus = 'pending' | 'approved' | 'rejected' | 'cancelled';

// Tipos de API
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Tipos de autenticação
export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface LoginResponse extends ApiResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Tipos de dashboard
export interface DashboardStats {
  today_records: TimeRecord[];
  weekly_hours: number;
  monthly_hours: number;
  pending_adjustments: number;
  last_record?: TimeRecord;
  next_expected_record?: {
    type: TimeRecordType;
    expected_time: string;
  };
}

// Tipos de relatório
export interface TimeReport {
  user_id: string;
  period: {
    start: string;
    end: string;
  };
  total_worked_hours: number;
  expected_hours: number;
  overtime_hours: number;
  undertime_hours: number;
  records: TimeRecord[];
  adjustments: AdjustmentRequest[];
}

// Tipos de formulário
export interface TimeRecordForm {
  type: TimeRecordType;
  timestamp: Date;
  location?: Location;
  photo?: File;
}

export interface AdjustmentRequestForm {
  original_record_id?: string;
  requested_timestamp: Date;
  requested_type: TimeRecordType;
  reason: string;
  attachments?: File[];
}

// Tipos de notificação
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: string;
  read: boolean;
}

// Tipos de configuração
export interface AppConfig {
  api_url: string;
  app_name: string;
  version: string;
  features: {
    photo_required: boolean;
    gps_required: boolean;
    face_detection: boolean;
    offline_mode: boolean;
  };
}

// Tipos de erro
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

// Tipos de filtro
export interface TimeRecordFilter {
  start_date?: string;
  end_date?: string;
  type?: TimeRecordType;
  user_id?: string;
}

export interface AdjustmentFilter {
  status?: AdjustmentStatus;
  start_date?: string;
  end_date?: string;
  user_id?: string;
}

// Tipos de paginação
export interface PaginationParams {
  page: number;
  limit: number;
}

// Tipos de tema
export type Theme = 'light' | 'dark' | 'system';

// Tipos de permissão
export interface UserPermissions {
  can_view_own_records: boolean;
  can_create_time_records: boolean;
  can_request_adjustments: boolean;
  can_view_team_records: boolean;
  can_approve_adjustments: boolean;
  can_manage_users: boolean;
  can_export_reports: boolean;
  can_view_audit_logs: boolean;
  can_manage_system: boolean;
}

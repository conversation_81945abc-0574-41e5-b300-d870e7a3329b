import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';
import { 
  ApiResponse, 
  LoginRequest, 
  LoginResponse, 
  User,
  TimeRecord,
  AdjustmentRequest,
  DashboardStats,
  TimeRecordForm,
  AdjustmentRequestForm,
  PaginatedResponse,
  TimeRecordFilter,
  AdjustmentFilter,
  PaginationParams
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000';
    
    this.api = axios.create({
      baseURL: `${this.baseURL}/api/v1`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor para adicionar token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor para tratar erros
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Se o token expirou, tentar renovar
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = localStorage.getItem('refresh_token');
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              localStorage.setItem('access_token', response.access_token);
              localStorage.setItem('refresh_token', response.refresh_token);
              
              // Repetir a requisição original
              originalRequest.headers.Authorization = `Bearer ${response.access_token}`;
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            // Se não conseguir renovar, fazer logout
            this.logout();
            window.location.href = '/login';
          }
        }

        // Mostrar erro para o usuário
        const message = error.response?.data?.message || 'Erro interno do servidor';
        toast.error(message);

        return Promise.reject(error);
      }
    );
  }

  // Métodos de autenticação
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.api.post<LoginResponse>('/auth/login', credentials);
    return response.data;
  }

  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    const response = await this.api.post<LoginResponse>('/auth/refresh', {
      refresh_token: refreshToken,
    });
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } catch (error) {
      // Ignorar erros de logout
    } finally {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.api.get<ApiResponse<User>>('/auth/me');
    return response.data.data!;
  }

  async changePassword(data: {
    current_password: string;
    new_password: string;
    confirm_password: string;
  }): Promise<void> {
    await this.api.post('/auth/change-password', data);
  }

  // Métodos de usuários
  async getUsers(): Promise<User[]> {
    const response = await this.api.get<ApiResponse<User[]>>('/users/');
    return response.data.data!;
  }

  async getUserProfile(): Promise<User> {
    const response = await this.api.get<ApiResponse<User>>('/users/me');
    return response.data.data!;
  }

  async updateUserProfile(data: Partial<User>): Promise<User> {
    const response = await this.api.patch<ApiResponse<User>>('/users/me', data);
    return response.data.data!;
  }

  // Métodos de registros de ponto
  async getTimeRecords(
    filters?: TimeRecordFilter,
    pagination?: PaginationParams
  ): Promise<PaginatedResponse<TimeRecord>> {
    const params = { ...filters, ...pagination };
    const response = await this.api.get<PaginatedResponse<TimeRecord>>('/time-records/', { params });
    return response.data;
  }

  async createTimeRecord(data: TimeRecordForm): Promise<TimeRecord> {
    const formData = new FormData();
    
    formData.append('type', data.type);
    formData.append('timestamp', data.timestamp.toISOString());
    
    if (data.location) {
      formData.append('latitude', data.location.latitude.toString());
      formData.append('longitude', data.location.longitude.toString());
      formData.append('address', data.location.address);
      if (data.location.accuracy) {
        formData.append('accuracy', data.location.accuracy.toString());
      }
    }
    
    if (data.photo) {
      formData.append('photo', data.photo);
    }

    const response = await this.api.post<ApiResponse<TimeRecord>>('/time-records/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data.data!;
  }

  async getTimeRecord(id: string): Promise<TimeRecord> {
    const response = await this.api.get<ApiResponse<TimeRecord>>(`/time-records/${id}`);
    return response.data.data!;
  }

  // Métodos de solicitações de ajuste
  async getAdjustmentRequests(
    filters?: AdjustmentFilter,
    pagination?: PaginationParams
  ): Promise<PaginatedResponse<AdjustmentRequest>> {
    const params = { ...filters, ...pagination };
    const response = await this.api.get<PaginatedResponse<AdjustmentRequest>>('/adjustments/', { params });
    return response.data;
  }

  async createAdjustmentRequest(data: AdjustmentRequestForm): Promise<AdjustmentRequest> {
    const formData = new FormData();
    
    if (data.original_record_id) {
      formData.append('original_record_id', data.original_record_id);
    }
    
    formData.append('requested_timestamp', data.requested_timestamp.toISOString());
    formData.append('requested_type', data.requested_type);
    formData.append('reason', data.reason);
    
    if (data.attachments) {
      data.attachments.forEach((file, index) => {
        formData.append(`attachments[${index}]`, file);
      });
    }

    const response = await this.api.post<ApiResponse<AdjustmentRequest>>('/adjustments/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data.data!;
  }

  async approveAdjustmentRequest(id: string, notes?: string): Promise<AdjustmentRequest> {
    const response = await this.api.post<ApiResponse<AdjustmentRequest>>(`/adjustments/${id}/approve`, {
      notes,
    });
    return response.data.data!;
  }

  async rejectAdjustmentRequest(id: string, notes: string): Promise<AdjustmentRequest> {
    const response = await this.api.post<ApiResponse<AdjustmentRequest>>(`/adjustments/${id}/reject`, {
      notes,
    });
    return response.data.data!;
  }

  // Métodos de dashboard
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await this.api.get<ApiResponse<DashboardStats>>('/dashboard/stats');
    return response.data.data!;
  }

  // Métodos de relatórios
  async exportTimeReport(
    format: 'pdf' | 'excel' | 'csv',
    filters?: TimeRecordFilter
  ): Promise<Blob> {
    const params = { format, ...filters };
    const response = await this.api.get('/reports/export', {
      params,
      responseType: 'blob',
    });
    return response.data;
  }

  // Métodos de health check
  async healthCheck(): Promise<any> {
    const response = await this.api.get('/health/');
    return response.data;
  }

  // Método para upload de arquivos
  async uploadFile(file: File, path: string = 'general'): Promise<string> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('path', path);

    const response = await this.api.post<ApiResponse<{ file_url: string }>>('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data.data!.file_url;
  }
}

// Instância singleton
export const apiService = new ApiService();
export default apiService;

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';

// Stores
import { useAuthStore } from './store/authStore';
import { useThemeStore } from './store/themeStore';

// Temporary placeholder components
const LoginPage = () => <div className="p-8">Login Page - Em desenvolvimento</div>;
const DashboardPage = () => <div className="p-8">Dashboard - Em desenvolvimento</div>;
const TimeRecordsPage = () => <div className="p-8">Registros de Ponto - Em desenvolvimento</div>;
const AdjustmentsPage = () => <div className="p-8">Solicitações - Em desenvolvimento</div>;
const ProfilePage = () => <div className="p-8">Perfil - Em desenvolvimento</div>;
const AuthLayout = ({ children }: { children: React.ReactNode }) => <div className="min-h-screen flex items-center justify-center bg-gray-50">{children}</div>;
const AppLayout = ({ children }: { children: React.ReactNode }) => <div className="min-h-screen bg-gray-50">{children}</div>;
const LoadingSpinner = () => <div className="flex items-center justify-center min-h-screen"><div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div></div>;

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <Routes>
            <Route path="/login" element={<AuthLayout><LoginPage /></AuthLayout>} />
            <Route path="/dashboard" element={<AppLayout><DashboardPage /></AppLayout>} />
            <Route path="/time-records" element={<AppLayout><TimeRecordsPage /></AppLayout>} />
            <Route path="/adjustments" element={<AppLayout><AdjustmentsPage /></AppLayout>} />
            <Route path="/profile" element={<AppLayout><ProfilePage /></AppLayout>} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
          <Toaster position="top-right" />
        </div>
      </Router>
    </QueryClientProvider>
  );
}

export default App;

# Configurações do Banco de Dados
DATABASE_URL=postgresql://ponto_user:ponto_password@localhost:5432/ponto_db
POSTGRES_DB=ponto_db
POSTGRES_USER=ponto_user
POSTGRES_PASSWORD=ponto_password

# Redis
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# MinIO (armazenamento de arquivos)
MINIO_ENDPOINT=http://localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET=ponto-files
MINIO_SECURE=false

# API URLs
BACKEND_URL=http://localhost:8000
FRONTEND_URL=http://localhost:5173
ADMIN_URL=http://localhost:5174

# Google Maps API (para geolocalização)
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Configurações de Email (para notificações)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_TLS=true

# Configurações de Segurança
BCRYPT_ROUNDS=12
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Configurações CLT
WORK_HOURS_PER_DAY=8
LUNCH_BREAK_MINUTES=60
MAX_OVERTIME_HOURS_PER_MONTH=40
TOLERANCE_MINUTES=10

# Configurações LGPD
PHOTO_RETENTION_MONTHS=6
DATA_EXPORT_FORMAT=json

# Configurações de Geolocalização
GEOFENCE_RADIUS_METERS=100
GPS_ACCURACY_THRESHOLD=50

# Ambiente
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Configurações de Upload
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=image/jpeg,image/png,application/pdf

# Configurações de Face Recognition
FACE_DETECTION_CONFIDENCE=0.8
LIVENESS_DETECTION=true

# Configurações de Backup
BACKUP_RETENTION_DAYS=30
AUTO_BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *

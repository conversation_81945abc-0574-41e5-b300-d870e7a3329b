# Arquivos de ambiente
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Backend específico
backend/.env
backend/uploads/
backend/logs/
backend/alembic/versions/*.py
!backend/alembic/versions/__init__.py

# Node.js
node_modules/
.npm
.eslintcache

# Frontend específico
frontend-web/dist/
frontend-web/node_modules/
frontend-web/.env
frontend-web/.vite/

# Admin panel específico
admin-panel/dist/
admin-panel/node_modules/
admin-panel/.env
admin-panel/.vite/

# Flutter/Dart
mobile-app/.dart_tool/
mobile-app/.flutter-plugins
mobile-app/.flutter-plugins-dependencies
mobile-app/.packages
mobile-app/.pub-cache/
mobile-app/.pub/
mobile-app/build/
mobile-app/ios/Pods/
mobile-app/ios/.symlinks/
mobile-app/ios/Flutter/Flutter.framework
mobile-app/ios/Flutter/Flutter.podspec
mobile-app/ios/Runner/GeneratedPluginRegistrant.*
mobile-app/android/.gradle
mobile-app/android/captures/
mobile-app/android/gradlew
mobile-app/android/gradlew.bat
mobile-app/android/local.properties
mobile-app/android/**/GeneratedPluginRegistrant.java
mobile-app/android/key.properties
*.jks

# IDEs e Editores
.vscode/
.idea/
*.swp
*.swo
*~
.spyderproject
.spyproject
.ropeproject
.dmypy.json
dmypy.json

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Banco de dados
*.sqlite
*.db

# Certificados e chaves
*.pem
*.key
*.crt
*.p12

# Arquivos temporários
tmp/
temp/
*.tmp

# Coverage reports
coverage/
*.lcov

# Documentação gerada
docs/api/

# Arquivos de backup
*.bak
*.backup

# Arquivos de teste
test-results/
screenshots/

# Arquivos específicos do projeto
uploads/
storage/
files/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

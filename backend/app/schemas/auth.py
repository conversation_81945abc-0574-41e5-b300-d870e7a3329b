from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
import uuid

from .common import BaseResponse


class LoginRequest(BaseModel):
    """Schema para login"""
    email: EmailStr = Field(..., description="Email do usuário")
    password: str = Field(..., min_length=8, max_length=128, description="Senha")
    remember_me: bool = Field(False, description="Lembrar login")


class LoginResponse(BaseResponse):
    """Schema de resposta do login"""
    access_token: str = Field(..., description="Token de acesso")
    refresh_token: str = Field(..., description="Token de refresh")
    token_type: str = Field("bearer", description="Tipo do token")
    expires_in: int = Field(..., description="Tempo de expiração em segundos")
    user: dict = Field(..., description="Dados do usuário")


class RefreshTokenRequest(BaseModel):
    """Schema para refresh token"""
    refresh_token: str = Field(..., description="Token de refresh")


class RefreshTokenResponse(BaseResponse):
    """Schema de resposta do refresh token"""
    access_token: str = Field(..., description="Novo token de acesso")
    refresh_token: str = Field(..., description="Novo token de refresh")
    token_type: str = Field("bearer", description="Tipo do token")
    expires_in: int = Field(..., description="Tempo de expiração em segundos")


class ChangePasswordRequest(BaseModel):
    """Schema para alteração de senha"""
    current_password: str = Field(..., min_length=8, description="Senha atual")
    new_password: str = Field(..., min_length=8, max_length=128, description="Nova senha")
    confirm_password: str = Field(..., min_length=8, max_length=128, description="Confirmação da nova senha")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Senhas não conferem')
        return v
    
    @validator('new_password')
    def validate_password_strength(cls, v):
        """Validar força da senha"""
        if len(v) < 8:
            raise ValueError('Senha deve ter pelo menos 8 caracteres')
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)
        
        if not (has_upper and has_lower and has_digit and has_special):
            raise ValueError(
                'Senha deve conter pelo menos: 1 letra maiúscula, '
                '1 letra minúscula, 1 número e 1 caractere especial'
            )
        
        return v


class ResetPasswordRequest(BaseModel):
    """Schema para solicitação de reset de senha"""
    email: EmailStr = Field(..., description="Email do usuário")


class ResetPasswordConfirm(BaseModel):
    """Schema para confirmação de reset de senha"""
    token: str = Field(..., description="Token de reset")
    new_password: str = Field(..., min_length=8, max_length=128, description="Nova senha")
    confirm_password: str = Field(..., min_length=8, max_length=128, description="Confirmação da nova senha")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Senhas não conferem')
        return v


class TokenPayload(BaseModel):
    """Schema do payload do token"""
    sub: Optional[str] = None
    exp: Optional[int] = None
    type: Optional[str] = None


class UserPermissions(BaseModel):
    """Schema de permissões do usuário"""
    can_view_own_records: bool = True
    can_create_time_records: bool = True
    can_request_adjustments: bool = True
    can_view_team_records: bool = False
    can_approve_adjustments: bool = False
    can_manage_users: bool = False
    can_export_reports: bool = False
    can_view_audit_logs: bool = False
    can_manage_system: bool = False


class AuthenticatedUser(BaseModel):
    """Schema do usuário autenticado"""
    id: uuid.UUID
    email: str
    name: str
    role: str
    is_active: bool
    permissions: UserPermissions
    
    class Config:
        from_attributes = True

from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List
from datetime import datetime
import uuid

from .common import BaseResponse, WorkScheduleSchema, NotificationSettingsSchema
from app.models.user import UserRole


class UserBase(BaseModel):
    """Schema base do usuário"""
    email: EmailStr = Field(..., description="Email do usuário")
    name: str = Field(..., min_length=2, max_length=255, description="Nome completo")
    phone: Optional[str] = Field(None, max_length=20, description="Telefone")
    employee_id: Optional[str] = Field(None, max_length=50, description="Matrícula")
    department: Optional[str] = Field(None, max_length=100, description="Departamento")
    position: Optional[str] = Field(None, max_length=100, description="Cargo")


class UserCreate(UserBase):
    """Schema para criação de usuário"""
    cpf: str = Field(..., min_length=11, max_length=11, description="CPF (apenas números)")
    password: str = Field(..., min_length=8, max_length=128, description="Senha")
    role: UserRole = Field(UserRole.EMPLOYEE, description="Papel do usuário")
    work_schedule: Optional[WorkScheduleSchema] = Field(None, description="Horário de trabalho")
    
    @validator('cpf')
    def validate_cpf(cls, v):
        """Validar CPF"""
        from app.core.security import validate_cpf
        
        # Remove caracteres não numéricos
        cpf_digits = ''.join(filter(str.isdigit, v))
        
        if not validate_cpf(cpf_digits):
            raise ValueError('CPF inválido')
        
        return cpf_digits
    
    @validator('password')
    def validate_password_strength(cls, v):
        """Validar força da senha"""
        if len(v) < 8:
            raise ValueError('Senha deve ter pelo menos 8 caracteres')
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)
        
        if not (has_upper and has_lower and has_digit and has_special):
            raise ValueError(
                'Senha deve conter pelo menos: 1 letra maiúscula, '
                '1 letra minúscula, 1 número e 1 caractere especial'
            )
        
        return v


class UserUpdate(BaseModel):
    """Schema para atualização de usuário"""
    name: Optional[str] = Field(None, min_length=2, max_length=255, description="Nome completo")
    phone: Optional[str] = Field(None, max_length=20, description="Telefone")
    department: Optional[str] = Field(None, max_length=100, description="Departamento")
    position: Optional[str] = Field(None, max_length=100, description="Cargo")
    work_schedule: Optional[WorkScheduleSchema] = Field(None, description="Horário de trabalho")
    notification_settings: Optional[NotificationSettingsSchema] = Field(None, description="Configurações de notificação")


class UserUpdateByAdmin(UserUpdate):
    """Schema para atualização de usuário pelo admin"""
    email: Optional[EmailStr] = Field(None, description="Email do usuário")
    employee_id: Optional[str] = Field(None, max_length=50, description="Matrícula")
    role: Optional[UserRole] = Field(None, description="Papel do usuário")
    is_active: Optional[bool] = Field(None, description="Status ativo")


class UserResponse(BaseModel):
    """Schema de resposta do usuário"""
    id: uuid.UUID
    email: str
    name: str
    cpf_masked: str  # CPF mascarado para exibição
    phone: Optional[str]
    employee_id: Optional[str]
    role: str
    department: Optional[str]
    position: Optional[str]
    is_active: bool
    is_verified: bool
    work_schedule: Optional[dict]
    notification_settings: Optional[dict]
    last_login: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserListResponse(BaseResponse):
    """Schema de resposta da lista de usuários"""
    data: List[UserResponse]
    total: int


class UserDetailResponse(BaseResponse):
    """Schema de resposta de detalhes do usuário"""
    data: UserResponse


class UserStatsResponse(BaseModel):
    """Schema de estatísticas do usuário"""
    total_records_today: int = Field(..., description="Registros de hoje")
    total_records_week: int = Field(..., description="Registros da semana")
    total_records_month: int = Field(..., description="Registros do mês")
    worked_hours_today: float = Field(..., description="Horas trabalhadas hoje")
    worked_hours_week: float = Field(..., description="Horas trabalhadas na semana")
    worked_hours_month: float = Field(..., description="Horas trabalhadas no mês")
    overtime_hours_month: float = Field(..., description="Horas extras no mês")
    pending_adjustments: int = Field(..., description="Ajustes pendentes")
    last_record: Optional[dict] = Field(None, description="Último registro")


class ConsentUpdate(BaseModel):
    """Schema para atualização de consentimentos LGPD"""
    data_consent: bool = Field(..., description="Consentimento para tratamento de dados")
    photo_consent: bool = Field(..., description="Consentimento para uso de fotos")


class DataExportRequest(BaseModel):
    """Schema para solicitação de exportação de dados"""
    format: str = Field("json", regex="^(json|csv|pdf)$", description="Formato da exportação")
    include_photos: bool = Field(False, description="Incluir fotos na exportação")
    date_range: Optional[dict] = Field(None, description="Intervalo de datas")

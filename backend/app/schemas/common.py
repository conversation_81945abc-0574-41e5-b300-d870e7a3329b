from pydantic import BaseModel, Field
from typing import Optional, List, Any, Generic, TypeVar
from datetime import datetime
import uuid

T = TypeVar('T')


class BaseResponse(BaseModel):
    """Resposta base da API"""
    success: bool = True
    message: Optional[str] = None


class ErrorResponse(BaseResponse):
    """Resposta de erro"""
    success: bool = False
    error_type: Optional[str] = None
    errors: Optional[List[dict]] = None


class PaginationParams(BaseModel):
    """Parâmetros de paginação"""
    page: int = Field(1, ge=1, description="Número da página")
    limit: int = Field(20, ge=1, le=100, description="Itens por página")
    
    @property
    def offset(self) -> int:
        return (self.page - 1) * self.limit


class PaginationMeta(BaseModel):
    """Metadados de paginação"""
    page: int
    limit: int
    total: int
    total_pages: int
    has_next: bool
    has_prev: bool


class PaginatedResponse(BaseResponse, Generic[T]):
    """Resposta paginada"""
    data: List[T]
    pagination: PaginationMeta


class LocationSchema(BaseModel):
    """Schema de localização"""
    latitude: float = Field(..., ge=-90, le=90, description="Latitude")
    longitude: float = Field(..., ge=-180, le=180, description="Longitude")
    address: Optional[str] = Field(None, max_length=500, description="Endereço")
    accuracy: Optional[float] = Field(None, ge=0, description="Precisão em metros")


class DeviceInfoSchema(BaseModel):
    """Schema de informações do dispositivo"""
    ip: Optional[str] = Field(None, description="Endereço IP")
    user_agent: Optional[str] = Field(None, description="User Agent")
    device_id: Optional[str] = Field(None, description="ID do dispositivo")
    platform: Optional[str] = Field(None, description="Plataforma (web, android, ios)")


class FileUploadResponse(BaseResponse):
    """Resposta de upload de arquivo"""
    file_url: str = Field(..., description="URL do arquivo")
    file_name: str = Field(..., description="Nome do arquivo")
    file_size: int = Field(..., description="Tamanho do arquivo em bytes")
    content_type: str = Field(..., description="Tipo de conteúdo")


class HealthCheckResponse(BaseModel):
    """Resposta do health check"""
    status: str = Field(..., description="Status da aplicação")
    timestamp: float = Field(..., description="Timestamp da verificação")
    environment: str = Field(..., description="Ambiente")
    database: bool = Field(..., description="Status do banco de dados")
    redis: bool = Field(..., description="Status do Redis")
    minio: bool = Field(..., description="Status do MinIO")


class AuditLogSchema(BaseModel):
    """Schema de log de auditoria"""
    id: uuid.UUID
    user_id: Optional[uuid.UUID]
    action: str
    entity_type: str
    entity_id: Optional[uuid.UUID]
    old_values: Optional[dict]
    new_values: Optional[dict]
    ip_address: Optional[str]
    user_agent: Optional[str]
    timestamp: datetime
    
    class Config:
        from_attributes = True


class DateRangeSchema(BaseModel):
    """Schema de intervalo de datas"""
    start_date: datetime = Field(..., description="Data de início")
    end_date: datetime = Field(..., description="Data de fim")
    
    def validate_range(self):
        """Validar se a data de fim é posterior à de início"""
        if self.end_date <= self.start_date:
            raise ValueError("Data de fim deve ser posterior à data de início")


class WorkScheduleSchema(BaseModel):
    """Schema de horário de trabalho"""
    name: str = Field(..., max_length=100, description="Nome do horário")
    start_time: str = Field(..., regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$", description="Horário de entrada (HH:MM)")
    end_time: str = Field(..., regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$", description="Horário de saída (HH:MM)")
    lunch_start_time: str = Field(..., regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$", description="Início do almoço (HH:MM)")
    lunch_end_time: str = Field(..., regex=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$", description="Fim do almoço (HH:MM)")
    work_days: List[int] = Field(..., description="Dias da semana (0-6, domingo-sábado)")
    tolerance_minutes: int = Field(10, ge=0, le=60, description="Tolerância em minutos")


class NotificationSettingsSchema(BaseModel):
    """Schema de configurações de notificação"""
    email_enabled: bool = Field(True, description="Notificações por email")
    push_enabled: bool = Field(True, description="Notificações push")
    sms_enabled: bool = Field(False, description="Notificações por SMS")
    reminder_before_shift: int = Field(30, ge=0, description="Lembrete antes do turno (minutos)")
    overtime_alert: bool = Field(True, description="Alerta de hora extra")

from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager
import time
import logging

from app.core.config import settings
from app.core.database import engine, Base
from app.api.api_v1.api import api_router
from app.core.exceptions import setup_exception_handlers

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gerenciar ciclo de vida da aplicação"""
    # Startup
    logger.info("🚀 Iniciando Sistema de Ponto Eletrônico CLT")
    
    # Criar tabelas do banco de dados
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("✅ Banco de dados inicializado")
    
    yield
    
    # Shutdown
    logger.info("🛑 Encerrando aplicação")


# <PERSON>riar instância do FastAPI
app = FastAPI(
    title="Ponto Eletrônico CLT API",
    description="API do Sistema de Ponto Eletrônico em conformidade com a CLT brasileira",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
    lifespan=lifespan,
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Middleware de hosts confiáveis
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)


# Middleware para logging de requests
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    # Log da requisição
    logger.info(f"📥 {request.method} {request.url}")
    
    response = await call_next(request)
    
    # Log da resposta
    process_time = time.time() - start_time
    logger.info(
        f"📤 {request.method} {request.url} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.4f}s"
    )
    
    return response


# Configurar handlers de exceção
setup_exception_handlers(app)

# Incluir rotas da API
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """Endpoint raiz com informações da API"""
    return {
        "name": "Ponto Eletrônico CLT API",
        "version": "1.0.0",
        "description": "API do Sistema de Ponto Eletrônico em conformidade com a CLT brasileira",
        "environment": settings.ENVIRONMENT,
        "docs": "/docs" if settings.ENVIRONMENT == "development" else "Disabled in production",
        "features": [
            "🔐 Autenticação JWT",
            "📱 Registro de ponto com GPS e foto",
            "👤 Validação facial anti-fraude",
            "📋 Solicitações de ajuste",
            "📊 Relatórios CLT",
            "📤 Exportação eSocial",
            "🔍 Audit trail completo",
            "🛡️ Conformidade LGPD",
        ],
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "environment": settings.ENVIRONMENT,
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.ENVIRONMENT == "development",
        log_level="info",
    )

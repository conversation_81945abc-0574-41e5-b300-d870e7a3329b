version: '3.8'

services:
  # Banco de dados PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: ponto-postgres
    environment:
      POSTGRES_DB: ponto_db
      POSTGRES_USER: ponto_user
      POSTGRES_PASSWORD: ponto_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ponto-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ponto_user -d ponto_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis para cache e sessões
  redis:
    image: redis:7-alpine
    container_name: ponto-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ponto-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Min<PERSON> para armazenamento de arquivos
  minio:
    image: minio/minio:latest
    container_name: ponto-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - ponto-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Backend API (FastAPI)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ponto-backend
    environment:
      - ENVIRONMENT=development
      - DATABASE_URL=****************************************************/ponto_db
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key
      - MINIO_ENDPOINT=http://minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - MINIO_BUCKET=ponto-files
      - GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    volumes:
      - ./backend:/app
    networks:
      - ponto-network
    restart: unless-stopped

  # Frontend Web (React)
  frontend-web:
    build:
      context: ./frontend-web
      dockerfile: Dockerfile
    container_name: ponto-frontend-web
    environment:
      - VITE_API_URL=http://localhost:8000
    ports:
      - "5173:5173"
    depends_on:
      - backend
    volumes:
      - ./frontend-web:/app
      - /app/node_modules
    networks:
      - ponto-network
    restart: unless-stopped

  # Painel Administrativo
  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile
    container_name: ponto-admin-panel
    environment:
      - VITE_API_URL=http://localhost:8000
    ports:
      - "5174:5173"
    depends_on:
      - backend
    volumes:
      - ./admin-panel:/app
      - /app/node_modules
    networks:
      - ponto-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  minio_data:

networks:
  ponto-network:
    driver: bridge

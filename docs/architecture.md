# Arquitetura do Sistema de Ponto Eletrônico

## 📋 Visão Geral

O sistema é estruturado como um monorepo com arquitetura de microserviços, garantindo escalabilidade, manutenibilidade e conformidade com a CLT brasileira.

## 🏗️ Componentes Principais

### 1. Backend API (FastAPI + Python)
- **Responsabilidade**: Lógica de negócio, autenticação, validações CLT
- **Tecnologias**: FastAPI, Python 3.11+, SQLAlchemy, Alembic
- **Portas**: 8000 (API REST)

### 2. Frontend Web (React + TypeScript)
- **Responsabilidade**: Interface para funcionários
- **Tecnologias**: React 18, TypeScript, Tailwind CSS, Vite
- **Portas**: 5173 (desenvolvimento)

### 3. Admin Panel (React + TypeScript)
- **Responsabilidade**: Interface administrativa para RH
- **Tecnologias**: React 18, TypeScript, Tailwind CSS, Vite
- **Portas**: 5174 (desenvolvimento)

### 4. Mobile App (Flutter)
- **Responsabilidade**: Registro de ponto mobile
- **Tecnologias**: Flutter 3.0+, Dart, SQLite (offline)
- **Plataformas**: Android, iOS

### 5. Banco de Dados (PostgreSQL)
- **Responsabilidade**: Armazenamento persistente
- **Características**: ACID, audit trail, backup automático
- **Portas**: 5432

### 6. Cache (Redis)
- **Responsabilidade**: Cache, sessões, filas
- **Portas**: 6379

### 7. Armazenamento (MinIO)
- **Responsabilidade**: Fotos, documentos, backups
- **Portas**: 9000 (API), 9001 (Console)

## 🔄 Fluxo de Dados

```mermaid
graph TB
    A[Mobile App] --> B[Backend API]
    C[Frontend Web] --> B
    D[Admin Panel] --> B
    B --> E[PostgreSQL]
    B --> F[Redis]
    B --> G[MinIO]
    
    subgraph "Conformidade CLT"
        H[Audit Trail]
        I[Assinatura Digital]
        J[Validações]
    end
    
    B --> H
    B --> I
    B --> J
```

## 🔐 Segurança e Autenticação

### JWT + Refresh Tokens
- **Access Token**: 1 hora de validade
- **Refresh Token**: 7 dias de validade
- **Algoritmo**: HS256
- **Rotação automática**: Tokens renovados automaticamente

### Níveis de Acesso
1. **Funcionário**: Registro de ponto, consultas pessoais
2. **Supervisor**: Aprovação de ajustes da equipe
3. **RH**: Acesso completo, relatórios, configurações
4. **Admin**: Configurações do sistema

### Criptografia
- **Senhas**: bcrypt com 12 rounds
- **Dados sensíveis**: Fernet (symmetric encryption)
- **Comunicação**: HTTPS/TLS 1.3

## 📊 Modelo de Dados

### Entidades Principais

#### Users (Usuários)
```python
class User(Base):
    id: UUID
    email: str
    password_hash: str
    name: str
    cpf: str  # encrypted
    role: UserRole
    is_active: bool
    department: str
    position: str
    work_schedule_id: UUID
    created_at: datetime
    updated_at: datetime
```

#### TimeRecords (Registros de Ponto)
```python
class TimeRecord(Base):
    id: UUID
    user_id: UUID
    type: TimeRecordType
    timestamp: datetime
    latitude: float
    longitude: float
    address: str
    photo_url: str
    device_info: dict
    hash: str  # SHA-256
    is_manual: bool
    approved_by: UUID
    created_at: datetime
```

#### AdjustmentRequests (Solicitações de Ajuste)
```python
class AdjustmentRequest(Base):
    id: UUID
    user_id: UUID
    original_record_id: UUID
    requested_timestamp: datetime
    type: TimeRecordType
    reason: str
    attachments: list[str]
    status: AdjustmentStatus
    reviewed_by: UUID
    reviewed_at: datetime
    review_notes: str
    created_at: datetime
```

## 🔍 Audit Trail

### Eventos Auditados
- Todos os registros de ponto
- Alterações de dados pessoais
- Aprovações/rejeições de ajustes
- Acessos administrativos
- Exportações de dados

### Estrutura do Log
```python
class AuditLog(Base):
    id: UUID
    user_id: UUID
    action: str
    entity_type: str
    entity_id: UUID
    old_values: dict
    new_values: dict
    ip_address: str
    user_agent: str
    timestamp: datetime
```

## 📱 Arquitetura Mobile

### Offline-First
- **SQLite local**: Cache de dados essenciais
- **Sincronização**: Background sync quando online
- **Conflitos**: Resolução automática com timestamp

### Validação Facial
- **Google ML Kit**: Detecção facial on-device
- **Liveness Detection**: Detecção de foto/vídeo
- **Privacy**: Processamento local, não armazena biometria

### GPS e Geofencing
- **Precisão**: ±5 metros
- **Validação**: Dentro do perímetro da empresa
- **Fallback**: Endereço via Google Maps API

## 🚀 Deploy e Infraestrutura

### Desenvolvimento
```bash
docker-compose up -d
```

### Produção
- **Backend**: Docker Swarm ou Kubernetes
- **Frontend**: Nginx + Docker
- **Banco**: PostgreSQL com replicação
- **Cache**: Redis Cluster
- **Storage**: MinIO Cluster
- **Load Balancer**: Nginx/HAProxy

### Monitoramento
- **Logs**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Métricas**: Prometheus + Grafana
- **Alertas**: AlertManager
- **Health Checks**: FastAPI health endpoints

## 📋 Conformidade CLT

### Artigo 74 da CLT
- ✅ Registros eletrônicos com integridade
- ✅ Impossibilidade de alteração direta
- ✅ Identificação do empregado
- ✅ Data, hora e localização
- ✅ Extração de relatórios

### Portaria 671/2021 MTP
- ✅ Sistema de controle eletrônico
- ✅ Armazenamento seguro por 5 anos
- ✅ Relatórios para fiscalização
- ✅ Backup e recuperação

### LGPD Compliance
- ✅ Consentimento explícito
- ✅ Minimização de dados
- ✅ Direito ao esquecimento
- ✅ Portabilidade de dados
- ✅ Notificação de vazamentos

## 🔧 Tecnologias Detalhadas

### Backend (FastAPI)
- **Framework**: FastAPI 0.104+
- **ORM**: SQLAlchemy 2.0+
- **Migrations**: Alembic
- **Validation**: Pydantic v2
- **Authentication**: python-jose, passlib
- **Testing**: pytest, httpx

### Frontend (React)
- **Framework**: React 18
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **HTTP Client**: Axios
- **Testing**: Vitest, Testing Library

### Mobile (Flutter)
- **Framework**: Flutter 3.0+
- **State Management**: Riverpod
- **Local Storage**: Hive/SQLite
- **HTTP Client**: Dio
- **Camera**: camera plugin
- **Location**: geolocator plugin
